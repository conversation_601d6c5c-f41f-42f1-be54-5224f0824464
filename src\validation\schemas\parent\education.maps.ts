import { createOptionsKeyMap } from '@/validation/utils/form.utils';

// 1. Education Types
export const educationTypeMap = {
  school: { key: 'school', label: 'School (Class 1-12)' },
  degree: { key: 'degree', label: 'Degree' },
  other: { key: 'other', label: 'Other Achievement' },
} as const;

export const educationTypeOptions = createOptionsKeyMap(educationTypeMap);
export type IEducationTypeMap = keyof typeof educationTypeMap;

// 2. Score Types
export const scoreTypeMap = {
  percentage: { key: 'percentage', label: 'Percentage' },
  cgpa: { key: 'cgpa', label: 'CGPA' },
  grade: { key: 'grade', label: 'Grade' },
  other: { key: 'other', label: 'Other' },
} as const;

export const scoreTypeOptions = createOptionsKeyMap(scoreTypeMap);
export type IScoreTypeMap = keyof typeof scoreTypeMap;
