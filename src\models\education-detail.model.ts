import mongoose, { Document, Schema } from 'mongoose';
import { IEducationTypeMap, IScoreTypeMap, educationTypeMap, scoreTypeMap } from '@/validation/schemas/parent/education.maps';

export interface IEducationDetail {
  childProfileId: mongoose.Types.ObjectId; // Reference to child profile
  educationType: IEducationTypeMap;

  // School specific fields
  boardId?: mongoose.Types.ObjectId; // Reference to Board model
  classId?: mongoose.Types.ObjectId; // Reference to Class model
  schoolName?: string;

  // Degree specific fields
  degreeLevelId?: mongoose.Types.ObjectId; // Reference to DegreeLevel model
  degreeId?: mongoose.Types.ObjectId; // Reference to Degree model
  branchId?: mongoose.Types.ObjectId; // Reference to Branch model
  collegeName?: string;

  // Other achievement specific fields
  certificateName?: string;
  certificateFor?: string;
  certificateBy?: string;

  // Common fields
  location: string;
  startDate: Date;
  endDate?: Date;
  scoreType: IScoreTypeMap;
  obtainedValue: string;
  maximumValue: number;
  certificateNumber?: string;
  attachmentUrl?: string;
  isActive: boolean;
}

export interface EducationDetailDocument extends IEducationDetail, Document {
  createdAt: Date;
  updatedAt: Date;
}

const educationDetailSchema = new Schema<EducationDetailDocument>(
  {
    childProfileId: {
      type: Schema.Types.ObjectId,
      ref: 'ChildProfile',
      required: [true, 'Child profile ID is required'],
    },
    educationType: {
      type: String,
      enum: Object.keys(educationTypeMap),
      required: [true, 'Education type is required'],
    },

    // School specific fields
    boardId: {
      type: Schema.Types.ObjectId,
      ref: 'Board',
      required: function () {
        return this.educationType === 'school';
      },
    },
    classId: {
      type: Schema.Types.ObjectId,
      ref: 'Class',
      required: function () {
        return this.educationType === 'school';
      },
    },
    schoolName: {
      type: String,
      required: function () {
        return this.educationType === 'school';
      },
    },

    // Degree specific fields
    degreeLevelId: {
      type: Schema.Types.ObjectId,
      ref: 'DegreeLevel',
      required: function () {
        return this.educationType === 'degree';
      },
    },
    degreeId: {
      type: Schema.Types.ObjectId,
      ref: 'Degree',
      required: function () {
        return this.educationType === 'degree';
      },
    },
    branchId: {
      type: Schema.Types.ObjectId,
      ref: 'Branch',
      required: function () {
        return this.educationType === 'degree';
      },
    },
    collegeName: {
      type: String,
      required: function () {
        return this.educationType === 'degree';
      },
    },

    // Other achievement specific fields
    certificateName: {
      type: String,
      required: function () {
        return this.educationType === 'other';
      },
    },
    certificateFor: {
      type: String,
      required: function () {
        return this.educationType === 'other';
      },
    },
    certificateBy: {
      type: String,
      required: function () {
        return this.educationType === 'other';
      },
    },

    // Common fields
    location: {
      type: String,
      required: [true, 'Location is required'],
    },
    startDate: {
      type: Date,
      required: [true, 'Start date is required'],
    },
    endDate: {
      type: Date,
    },
    scoreType: {
      type: String,
      enum: Object.keys(scoreTypeMap),
      required: [true, 'Score type is required'],
    },
    obtainedValue: {
      type: String,
      required: [true, 'Obtained value is required'],
    },
    maximumValue: {
      type: Number,
      required: [true, 'Maximum value is required'],
    },
    certificateNumber: {
      type: String,
    },
    attachmentUrl: {
      type: String,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
  },
  {
    timestamps: true,
  }
);

// Add indexes to optimize queries
educationDetailSchema.index({ childProfileId: 1 });
educationDetailSchema.index({ educationType: 1 });
educationDetailSchema.index({ isActive: 1 });

// Add indexes for reference fields
educationDetailSchema.index({ boardId: 1 });
educationDetailSchema.index({ classId: 1 });
educationDetailSchema.index({ degreeLevelId: 1 });
educationDetailSchema.index({ degreeId: 1 });
educationDetailSchema.index({ branchId: 1 });

const EducationDetail = mongoose.models.EducationDetail || mongoose.model<EducationDetailDocument>('EducationDetail', educationDetailSchema);

export default EducationDetail;
