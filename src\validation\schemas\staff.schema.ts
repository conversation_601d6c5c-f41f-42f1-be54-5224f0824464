import { z } from 'zod';
import { emailSchema, PASSWORD_REGEX, PASSWORD_REQUIREMENTS, phoneSchema } from './common.schema';
import { adminRolesMap, IAdminRolesMap } from './maps';

// Create Staff Schema
export const createStaffSchema = z
  .object({
    fullName: z.string({ required_error: 'Full name is required' }).min(2, 'Full name must be at least 2 characters'),
    email: emailSchema,
    phone: phoneSchema.optional(),
    password: z
      .string({ required_error: 'Password is required' })
      .min(8, 'Password must be at least 8 characters')
      .regex(PASSWORD_REGEX, PASSWORD_REQUIREMENTS),
    confirmPassword: z.string({ required_error: 'Password confirmation is required' }),
    role: z.enum(Object.keys(adminRolesMap) as [IAdminRolesMap, ...IAdminRolesMap[]], {
      required_error: 'Staff role is required',
      invalid_type_error: 'Invalid staff role',
    }),
    profilePicture: z.string().optional(),
    isActive: z.boolean().default(true),
    permissions: z.array(z.string()).optional(),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords don't match",
    path: ['confirmPassword'],
  });

// Update Staff Schema
export const updateStaffSchema = z.object({
  fullName: z.string().min(2, 'Full name must be at least 2 characters').optional(),
  email: emailSchema.optional(),
  phone: phoneSchema.optional(),
  role: z
    .enum(Object.keys(adminRolesMap) as [IAdminRolesMap, ...IAdminRolesMap[]], {
      invalid_type_error: 'Invalid staff role',
    })
    .optional(),
  profilePicture: z.string().optional(),
  isActive: z.boolean().optional(),
  permissions: z.array(z.string()).optional(),
});

// Update Staff Password Schema
export const updateStaffPasswordSchema = z
  .object({
    currentPassword: z.string({ required_error: 'Current password is required' }),
    newPassword: z
      .string({ required_error: 'New password is required' })
      .min(8, 'Password must be at least 8 characters')
      .regex(PASSWORD_REGEX, PASSWORD_REQUIREMENTS),
    confirmPassword: z.string({ required_error: 'Password confirmation is required' }),
  })
  .refine((data) => data.newPassword === data.confirmPassword, {
    message: "Passwords don't match",
    path: ['confirmPassword'],
  });

// Staff Login Schema
export const staffLoginSchema = z.object({
  email: emailSchema,
  password: z.string({ required_error: 'Password is required' }).min(8, 'Password must be at least 8 characters'),
});

export type CreateStaffInput = z.infer<typeof createStaffSchema>;
export type UpdateStaffInput = z.infer<typeof updateStaffSchema>;
export type UpdateStaffPasswordInput = z.infer<typeof updateStaffPasswordSchema>;
export type StaffLoginInput = z.infer<typeof staffLoginSchema>;
