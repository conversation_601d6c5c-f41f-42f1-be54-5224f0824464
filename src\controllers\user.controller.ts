import { Request, Response } from 'express';
import { generateSessionId } from '@/utils/crypto.utils';
import User from '@/models/user.model';
import { StatusCodes } from '@/constants';
import { NotFoundError, BadRequestError, UnauthenticatedError } from '@/errors';
import { createSessionUser, createSessionCookie, validateData, updateAccountStatus, isProfileComplete } from '@/utils';
import { AggregateQueryManager } from '@/utils/aggregate.utils';
import { AuthenticatedRequest } from '@/types/express';
import { createFileUploader } from '@/utils/upload.utils';
import { isValidObjectId } from 'mongoose';
import { MAX_FILE_SIZE, ACCEPTED_IMAGE_TYPES } from '@/validation/schemas/common.schema';
import {
  updateUserPasswordSchema,
  UpdateUserAccountStatusInput,
  updateUserAccountStatusSchema,
  generalInfoSchema,
} from '@/validation/schemas/user.schema';

export const getAllUsers = async (req: Request, res: Response): Promise<void> => {
  const queryString = req.query;

  const baseAggregation = [{ $match: { role: { $ne: 'admin' } } }];

  const queryManager = new AggregateQueryManager({ model: User, queryString, baseAggregation }).filter().sort().paginate();

  const [users, pagination] = await Promise.all([queryManager.execute(), queryManager.getPaginationMetadata()]);

  res.status(StatusCodes.OK).json({ success: true, message: 'Users fetched successfully', count: users.length, data: { users, pagination } });
};

export const getSingleUser = async (req: Request, res: Response): Promise<void> => {
  const { userId } = req.params;

  if (!userId || !isValidObjectId(userId)) throw new BadRequestError('Please provide a valid user id');

  const user = await User.findById(userId);
  if (!user) throw new NotFoundError(`No User found with id: ${userId}`);

  res.status(StatusCodes.OK).json({ success: true, message: 'User fetched successfully', data: { user } });
};

export const showCurrentUser = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  const userId = req.user?.userId;

  const user = await User.findById(userId);
  if (!user) throw new NotFoundError('No User found for the current user.');

  const sessionUser = await createSessionUser(user);

  res.status(StatusCodes.OK).json({ success: true, message: 'Current user fetched successfully', data: { user: sessionUser } });
};

export const updateUserPassword = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  const userId = req.user?.userId;

  const validatedData = await validateData(updateUserPasswordSchema, req.body);
  const { oldPassword, newPassword } = validatedData;

  if (oldPassword === newPassword) throw new BadRequestError('Your old and new passwords are the same.');

  const user = await User.findById(userId).select('+password');
  if (!user) throw new NotFoundError('No User found for the current user.');

  const isPasswordMatch = await user.comparePassword(oldPassword);
  if (!isPasswordMatch) throw new UnauthenticatedError('Incorrect old password');

  user.password = newPassword;
  await user.save();

  res.status(StatusCodes.OK).json({ success: true, message: 'Password successfully changed.' });
};

export const updateUserAccountStatus = async (req: Request<{ userId: string }, {}, UpdateUserAccountStatusInput>, res: Response): Promise<void> => {
  const { userId } = req.params;

  if (!userId || !isValidObjectId(userId)) throw new BadRequestError('Please provide a valid user id');

  const validatedData = await validateData(updateUserAccountStatusSchema, req.body);
  const { accountStatus } = validatedData;

  const user = await User.findById(userId);
  if (!user) throw new NotFoundError(`No User found with id: ${userId}`);

  user.accountStatus = accountStatus;
  await user.save();

  res.status(StatusCodes.OK).json({
    success: true,
    message: `User account status updated to ${accountStatus} successfully`,
    data: {
      userId: user._id,
      accountStatus: user.accountStatus,
    },
  });
};

export const updateProfilePicture = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  const userId = req.user?.userId;
  const profilePicture = req.files?.profilePicture;

  if (!profilePicture) {
    throw new BadRequestError('Profile picture is required');
  }

  const user = await User.findById(userId);
  if (!user) throw new NotFoundError('No User found for the current user.');

  const fileUploader = createFileUploader('image');

  const file = Array.isArray(profilePicture) ? profilePicture[0] : profilePicture;

  if (file.size > MAX_FILE_SIZE) {
    throw new BadRequestError('File size must be less than 2MB');
  }

  if (!ACCEPTED_IMAGE_TYPES.includes(file.mimetype)) {
    throw new BadRequestError('File must be a valid image (JPEG, PNG, JPG, WEBP)');
  }

  const directory = `profile-pictures/${user._id.toString()}`;
  const fileName = `profile-picture-${Date.now()}`;

  const filePath = await fileUploader.uploadFile({ file, fileName, directory });

  user.profilePicture = filePath;

  const profileComplete = await isProfileComplete(user);
  await updateAccountStatus(user, { isProfileComplete: profileComplete });

  await user.save();
  const sessionUser = await createSessionUser(user);

  const sessionId = req.sessionId || generateSessionId();
  const token = await createSessionCookie({ res, user: sessionUser, sessionId });

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'Profile picture updated successfully',
    data: { user: sessionUser },
  });
};

export const getGeneralInfo = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  const userId = req.user?.userId;

  const user = await User.findById(userId);
  if (!user) throw new NotFoundError('No User found for the current user.');

  const generalInfo = {
    fullName: user.fullName,
    email: user.email,
    phone: user.phone,
    alternativeMobile: user.alternativeMobile,
    primaryWhatsApp: user.primaryWhatsApp,
    alternativeWhatsApp: user.alternativeWhatsApp,
    gender: user.gender,
    dateOfBirth: user.dateOfBirth,
    location: user.location,
    referralSource: user.referralSource,
    otherReferralSource: user.otherReferralSource,
    profilePicture: user.profilePicture,
    userType: user.userType,
    parentGuardianFullName: user.parentGuardianFullName,
  };

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'General info fetched successfully',
    data: { generalInfo },
  });
};

export const updateGeneralInfo = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  const userId = req.user?.userId;

  const validatedData = await validateData(generalInfoSchema, req.body);

  const user = await User.findById(userId);
  if (!user) throw new NotFoundError('No User found for the current user.');

  const fieldsToUpdate = [
    'fullName',
    'alternativeMobile',
    'primaryWhatsApp',
    'alternativeWhatsApp',
    'gender',
    'dateOfBirth',
    'location',
    'referralSource',
    'otherReferralSource',
    'parentGuardianFullName',
  ];

  fieldsToUpdate.forEach((field) => {
    if (validatedData[field as keyof typeof validatedData] !== undefined) {
      user[field] = validatedData[field as keyof typeof validatedData];
    }
  });

  if (!user.primaryWhatsApp && user.phone) {
    user.primaryWhatsApp = user.phone;
  }

  const profileComplete = await isProfileComplete(user);
  await updateAccountStatus(user, { isProfileComplete: profileComplete });

  await user.save();

  const sessionUser = await createSessionUser(user);
  const sessionId = req.sessionId || generateSessionId();
  const token = await createSessionCookie({ res, user: sessionUser, sessionId });

  res.status(StatusCodes.OK).json({
    success: true,
    message: 'General info updated successfully',
    data: { user: sessionUser, token },
  });
};
