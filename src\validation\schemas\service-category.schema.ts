import { z } from 'zod';
import { IServiceCategoryMap, serviceCategoryMap } from './education/index.maps';

// Service Category Schemas
export const createServiceCategorySchema = z.object({
  name: z.enum(Object.keys(serviceCategoryMap) as [IServiceCategoryMap, ...IServiceCategoryMap[]], {
    required_error: 'Service category name is required',
    invalid_type_error: 'Invalid service category',
  }),
  isActive: z.boolean().default(true),
});

export type CreateServiceCategoryInput = z.infer<typeof createServiceCategorySchema>;
export type UpdateServiceCategoryInput = Partial<CreateServiceCategoryInput>;
