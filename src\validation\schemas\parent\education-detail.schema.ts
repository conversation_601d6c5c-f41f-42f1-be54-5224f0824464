import { z } from 'zod';
import { objectIdSchema } from '../common.schema';
import { educationTypeMap, IEducationTypeMap, scoreTypeMap, IScoreTypeMap } from './education.maps';

const commonFieldsSchema = z.object({
  // Required fields
  childProfileId: objectIdSchema.describe('Child profile ID'),
  educationType: z.enum(Object.keys(educationTypeMap) as [IEducationTypeMap, ...IEducationTypeMap[]]).describe('Type of education'),
  location: z.string().min(1, 'Location is required').describe('Location where education was obtained'),
  startDate: z.coerce
    .date()
    .min(new Date('1900-01-01'), 'Date must be after 1900')
    .max(new Date(), 'Date cannot be in the future')
    .describe('Start date of education'),

  // Optional fields
  endDate: z.coerce.date().min(new Date('1900-01-01'), 'Date must be after 1900').optional().describe('End date of education (optional)'),
  certificateNumber: z.string().optional().describe('Certificate number (optional)'),
  attachmentUrl: z.any().optional().describe('URL to certificate attachment (optional)'),
});

const scoreValidationSchema = z
  .object({
    scoreType: z.enum(Object.keys(scoreTypeMap) as [IScoreTypeMap, ...IScoreTypeMap[]]).describe('Type of score'),
    obtainedValue: z.string().min(1, 'Obtained value is required').describe('Score value obtained'),
    maximumValue: z.coerce.number().min(1, 'Maximum value must be positive').describe('Maximum possible score value'),
  })
  .superRefine((data, ctx) => {
    const { scoreType, obtainedValue, maximumValue } = data;
    if (scoreType !== 'grade') {
      const score = parseFloat(obtainedValue);
      if (isNaN(score)) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: `${scoreTypeMap[scoreType].label} must be a valid number`,
          path: ['obtainedValue'],
        });
        return;
      }
    }

    switch (scoreType) {
      case 'percentage':
        const percentageValue = parseFloat(obtainedValue);
        if (percentageValue < 0 || percentageValue > 100) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'Percentage must be between 0 and 100',
            path: ['obtainedValue'],
          });
        }

        const maxPercentage = Number(maximumValue);
        if (maxPercentage > 100) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'Maximum value for percentage should be 100 or less',
            path: ['maximumValue'],
          });
        }
        break;

      case 'cgpa':
        const cgpaValue = parseFloat(obtainedValue);
        if (cgpaValue < 0 || cgpaValue > 10) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'CGPA must be between 0 and 10',
            path: ['obtainedValue'],
          });
        }

        const maxCGPA = Number(maximumValue);
        if (maxCGPA > 10) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'Maximum value for CGPA should be 10 or less',
            path: ['maximumValue'],
          });
        }
        break;
    }
  });

const schoolEducationSchema = z.object({
  educationType: z.literal('school').describe('School education type'),
  boardId: objectIdSchema.describe('School board ID'),
  classId: objectIdSchema.describe('Class/grade ID'),
  schoolName: z.string().min(1, 'School name is required').describe('Name of the school'),
});

const degreeEducationSchema = z.object({
  educationType: z.literal('degree').describe('Degree education type'),
  streamId: objectIdSchema.describe('Stream ID'),
  degreeLevelId: objectIdSchema.describe('Degree level ID'),
  degreeId: objectIdSchema.describe('Degree ID'),
  branchId: z.union([objectIdSchema, z.string()]).describe('Branch/specialization ID (optional)'),
  collegeName: z.string().min(1, 'College name is required').describe('Name of the college/university'),
});

const otherAchievementSchema = z.object({
  educationType: z.literal('other').describe('Other achievement type'),
  certificateName: z.string().min(1, 'Certificate name is required').describe('Name of the certificate/achievement'),
  certificateBy: z.string().min(1, 'Certificate by is required').describe('Issuing organization'),
  certificateFor: z.string().min(1, 'Certificate for is required').describe('Purpose/subject of the certificate'),
});

export const createEducationDetailSchema = z
  .intersection(commonFieldsSchema, scoreValidationSchema)
  .and(z.discriminatedUnion('educationType', [schoolEducationSchema, degreeEducationSchema, otherAchievementSchema]))
  .superRefine((data, ctx) => {
    if (data.location.trim() === '') {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Location cannot be empty',
        path: ['location'],
      });
    }

    if (data.obtainedValue.trim() === '') {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Obtained value cannot be empty',
        path: ['obtainedValue'],
      });
    }

    if (data.startDate && data.endDate) {
      if (data.endDate < data.startDate) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'End date must be after start date',
          path: ['endDate'],
        });
      }
    }

    // Type-specific validations
    switch (data.educationType) {
      case 'school':
        if (data.schoolName.trim() === '') {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'School name cannot be empty',
            path: ['schoolName'],
          });
        }
        break;
      case 'degree':
        if (data.collegeName.trim() === '') {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'College name cannot be empty',
            path: ['collegeName'],
          });
        }
        break;
      case 'other':
        if (data.certificateName.trim() === '') {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'Certificate name cannot be empty',
            path: ['certificateName'],
          });
        }
        if (data.certificateFor.trim() === '') {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'Certificate for cannot be empty',
            path: ['certificateFor'],
          });
        }
        if (data.certificateBy.trim() === '') {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'Certificate by cannot be empty',
            path: ['certificateBy'],
          });
        }
        break;
    }
  });

export const updateEducationDetailSchema = z
  .object({
    childProfileId: objectIdSchema.optional(),
    educationType: z.enum(Object.keys(educationTypeMap) as [IEducationTypeMap, ...IEducationTypeMap[]]).optional(),
    location: z.string().optional(),
    startDate: z.coerce.date().min(new Date('1900-01-01'), 'Date must be after 1900').optional(),
    endDate: z.coerce.date().min(new Date('1900-01-01'), 'Date must be after 1900').optional(),
    certificateNumber: z.string().optional(),
    attachmentUrl: z.any().optional(),
    scoreType: z.enum(Object.keys(scoreTypeMap) as [IScoreTypeMap, ...IScoreTypeMap[]]).optional(),
    obtainedValue: z.string().optional(),
    maximumValue: z.coerce.number().optional(),
    // School fields
    boardId: objectIdSchema.optional(),
    classId: objectIdSchema.optional(),
    schoolName: z.string().optional(),
    // Degree fields
    streamId: objectIdSchema.optional(),
    degreeLevelId: objectIdSchema.optional(),
    degreeId: objectIdSchema.optional(),
    branchId: z.union([objectIdSchema, z.string()]).optional(),
    collegeName: z.string().optional(),
    // Other achievement fields
    certificateName: z.string().optional(),
    certificateBy: z.string().optional(),
    certificateFor: z.string().optional(),
  })
  .superRefine((data, ctx) => {
    if (data.location !== undefined && data.location.trim() === '') {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Location cannot be empty',
        path: ['location'],
      });
    }

    if (data.obtainedValue !== undefined && data.obtainedValue.trim() === '') {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Obtained value cannot be empty',
        path: ['obtainedValue'],
      });
    }

    if (data.maximumValue !== undefined && data.maximumValue < 1) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Maximum value must be positive',
        path: ['maximumValue'],
      });
    }

    if (data.startDate !== undefined && data.endDate !== undefined) {
      if (data.endDate < data.startDate) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'End date must be after start date',
          path: ['endDate'],
        });
      }
    }

    if (data.educationType) {
      switch (data.educationType) {
        case 'school':
          if (data.schoolName !== undefined && data.schoolName.trim() === '') {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: 'School name cannot be empty',
              path: ['schoolName'],
            });
          }
          break;
        case 'degree':
          if (data.collegeName !== undefined && data.collegeName.trim() === '') {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: 'College name cannot be empty',
              path: ['collegeName'],
            });
          }
          break;
        case 'other':
          if (data.certificateName !== undefined && data.certificateName.trim() === '') {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: 'Certificate name cannot be empty',
              path: ['certificateName'],
            });
          }
          if (data.certificateFor !== undefined && data.certificateFor.trim() === '') {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: 'Certificate for cannot be empty',
              path: ['certificateFor'],
            });
          }
          if (data.certificateBy !== undefined && data.certificateBy.trim() === '') {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: 'Certificate by cannot be empty',
              path: ['certificateBy'],
            });
          }
          break;
      }
    }
  });

// Type definitions for TypeScript
export type CreateEducationDetailInput = z.infer<typeof createEducationDetailSchema>;
export type UpdateEducationDetailInput = z.infer<typeof updateEducationDetailSchema>;
