import { z } from 'zod';
import {
  ACCEPTED_IMAGE_TYPES,
  MAX_FILE_SIZE,
  PASSWORD_REGEX,
  PASSWORD_REQUIREMENTS,
  dateOfBirthSchema,
  fullNameSchema,
  genderSchema,
  phoneSchema,
} from './common.schema';
import { IAccountStatusMap, IAddressTypeMap, accountStatusMap, addressTypeMap } from './maps';

export const updateUserPasswordSchema = z
  .object({
    oldPassword: z.string({ required_error: 'Current password is required' }).min(1, 'Current password is required'),
    newPassword: z
      .string({ required_error: 'New password is required' })
      .min(8, 'Password must be at least 8 characters')
      .regex(PASSWORD_REGEX, PASSWORD_REQUIREMENTS),
    confirmPassword: z.string({ required_error: 'Password confirmation is required' }).min(8, 'Password must be at least 8 characters'),
  })
  .refine((data) => data.newPassword === data.confirmPassword, {
    message: 'Passwords do not match',
    path: ['confirmPassword'],
  })
  .refine((data) => data.oldPassword !== data.newPassword, {
    message: 'New password must be different from the current password',
    path: ['newPassword'],
  });

export const updateUserAccountStatusSchema = z.object({
  accountStatus: z.enum(Object.keys(accountStatusMap) as [IAccountStatusMap, ...IAccountStatusMap[]], {
    errorMap: () => ({ message: 'Invalid account status.' }),
  }),
});

export const generalInfoSchema = z.object({
  fullName: fullNameSchema,
  alternativeMobile: phoneSchema.optional(),
  primaryWhatsApp: phoneSchema.optional(),
  alternativeWhatsApp: phoneSchema.optional(),
  gender: genderSchema,
  dateOfBirth: dateOfBirthSchema,
  location: z.string().min(2, { message: 'Location must be at least 2 characters.' }),
  referralSource: z.string().optional(),
  otherReferralSource: z.string().optional(),
  parentGuardianFullName: z.string().min(2, { message: 'Parent/Guardian full name must be at least 2 characters.' }).optional(),
});

export const addressSchema = z.object({
  houseNo: z.string().min(1, { message: 'Please enter your house number.' }),
  locality: z.string().min(1, { message: 'Please enter your locality.' }),
  landmark: z.string().optional(),
  areaPinCode: z
    .string()
    .min(3, { message: 'PIN/ZIP code must be at least 3 characters.' })
    .max(12, { message: 'PIN/ZIP code cannot exceed 12 characters.' }),
  city: z.string().min(1, { message: 'Please enter your city.' }),
  district: z.string().min(1, { message: 'Please enter your district.' }),
  state: z.string().min(1, { message: 'Please enter your state.' }),
  addressType: z.enum(Object.keys(addressTypeMap) as [IAddressTypeMap, ...IAddressTypeMap[]], {
    errorMap: () => ({ message: `Invalid address type. Must be one of: ${Object.keys(addressTypeMap).join(', ')}` }),
  }),
  lat: z.number().optional(),
  lng: z.number().optional(),
});

export const profilePictureSchema = z.object({
  profilePicture: z
    .instanceof(File)
    .refine((file) => file.size <= MAX_FILE_SIZE, {
      message: 'File size must be less than 2MB',
    })
    .refine((file) => ACCEPTED_IMAGE_TYPES.includes(file.type), {
      message: 'File must be a valid image (JPEG, PNG, JPG, WEBP)',
    }),
});

export type ProfilePictureFormValues = z.infer<typeof profilePictureSchema>;
export type AddressFormValues = z.infer<typeof addressSchema>;
export type GeneralInfoFormValues = z.infer<typeof generalInfoSchema>;
export type UpdateUserPasswordInput = z.infer<typeof updateUserPasswordSchema>;
export type UpdateUserAccountStatusInput = z.infer<typeof updateUserAccountStatusSchema>;
