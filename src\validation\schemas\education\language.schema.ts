import { z } from 'zod';
import { objectIdSchema } from '../common.schema';

// Language Type Schemas
export const createLanguageTypeSchema = z.object({
  name: z.string({ required_error: 'Language type name is required' }).min(1, 'Language type name is required'),
  isActive: z.boolean().default(true),
});

export type CreateLanguageTypeInput = z.infer<typeof createLanguageTypeSchema>;
export type UpdateLanguageTypeInput = Partial<CreateLanguageTypeInput>;

// Language Schemas
export const createLanguageSchema = z.object({
  name: z.string({ required_error: 'Language name is required' }).min(1, 'Language name is required'),
  languageType: objectIdSchema,
  isActive: z.boolean().default(true),
});

export type CreateLanguageInput = z.infer<typeof createLanguageSchema>;
export type UpdateLanguageInput = Partial<CreateLanguageInput>;
