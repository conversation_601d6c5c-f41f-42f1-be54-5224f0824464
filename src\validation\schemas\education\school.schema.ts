import { z } from 'zod';
import { objectIdSchema } from '../common.schema';

// School Board Schemas
export const createBoardSchema = z.object({
  name: z.string({ required_error: 'Board name is required' }).min(1, 'Board name is required'),
  isActive: z.boolean().default(true),
});

export type CreateBoardInput = z.infer<typeof createBoardSchema>;
export type UpdateBoardInput = Partial<CreateBoardInput>;

// Class Schemas
export const createClassSchema = z.object({
  name: z.string({ required_error: 'Class name is required' }).min(1, 'Class name is required'),
  displayOrder: z.number({ required_error: 'Display order is required' }),
  board: objectIdSchema,
  isActive: z.boolean().default(true),
});

export type CreateClassInput = z.infer<typeof createClassSchema>;
export type UpdateClassInput = Partial<CreateClassInput>;

// Subject Schemas
export const createSubjectSchema = z.object({
  name: z.string({ required_error: 'Subject name is required' }).min(1, 'Subject name is required'),
  board: objectIdSchema,
  class: objectIdSchema,
  isActive: z.boolean().default(true),
});

export type CreateSubjectInput = z.infer<typeof createSubjectSchema>;
export type UpdateSubjectInput = Partial<CreateSubjectInput>;
